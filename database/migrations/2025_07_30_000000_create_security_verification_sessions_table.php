<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_verification_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->unique()->index(); // UUID for session identification
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('email'); // Store email at time of verification
            $table->string('verification_code'); // 6-digit code
            $table->integer('attempts')->default(0); // Failed attempts for this session
            $table->boolean('verified')->default(false); // Whether code was successfully verified
            $table->timestamp('verified_at')->nullable(); // When verification was completed
            $table->timestamp('expires_at')->index(); // When this session expires
            $table->string('ip_address', 45)->nullable(); // IP address for security
            $table->text('user_agent')->nullable(); // User agent for security
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'verified', 'expires_at']);
            $table->index(['session_id', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_verification_sessions');
    }
};

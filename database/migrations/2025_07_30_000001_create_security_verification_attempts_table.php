<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('security_verification_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('session_id')->nullable(); // Reference to verification session
            $table->string('attempted_code')->nullable(); // What code was attempted
            $table->boolean('successful')->default(false); // Whether attempt was successful
            $table->string('failure_reason')->nullable(); // Why it failed (expired, invalid, etc.)
            $table->string('ip_address', 45)->nullable(); // IP address for security
            $table->text('user_agent')->nullable(); // User agent for security
            $table->timestamps();

            // Indexes for performance and rate limiting
            $table->index(['user_id', 'created_at']);
            $table->index(['user_id', 'successful', 'created_at']);
            $table->index(['session_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('security_verification_attempts');
    }
};

<?php

namespace Domain\Payment\Enum;

enum PaymentSortableFields: string
{
    case Id = 'id';
    case CreatedAt = 'created_at';
    case UserId = 'user_id';
    case PaymentAmount = 'payment_amount';
    case Status = 'status';
    case ExternalTransactionId = 'external_transaction_id';

    /*********************************************************************
     * GET ALL SORTABLE FIELD VALUES
     *********************************************************************
     *
     * Returns array of all sortable field values for validation
     * and sorting purposes.
     *
     * @return array
     *
     *********************************************************************/
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /*********************************************************************
     * IS VALID SORT FIELD
     *********************************************************************
     *
     * Check if a given field is valid for sorting
     *
     * @param string $field
     * @return bool
     *
     *********************************************************************/
    public static function isValid(string $field): bool
    {
        return in_array($field, self::values());
    }
}

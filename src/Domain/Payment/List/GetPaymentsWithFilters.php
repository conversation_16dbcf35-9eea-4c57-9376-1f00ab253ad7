<?php

namespace Domain\Payment\List;

use App\Models\MarketplacePayment;
use App\Traits\Filters\DateFilterTrait;
use Illuminate\Pagination\LengthAwarePaginator;

/*********************************************************************
 * GET PAYMENTS WITH FILTERS
 *********************************************************************
 *
 * Retrieves marketplace payments with comprehensive filtering,
 * searching, sorting, and pagination capabilities.
 *
 * Responsibilities:
 * - Apply search filters across payment attributes
 * - Filter by payment status
 * - Apply date range filtering using DateFilterTrait
 * - Handle sorting with validation
 * - Eager load relationships efficiently
 * - Return paginated results
 *
 *********************************************************************/
class GetPaymentsWithFilters
{
    use DateFilterTrait;


    /*********************************************************************
     * GET FILTERED PAYMENTS
     *********************************************************************
     *
     * Retrieves payments with optional filters and pagination.
     * Uses model scopes for clean, reusable filtering logic.
     *
     * @param array $filters - Filter and pagination parameters
     * @return LengthAwarePaginator - Paginated payment results
     *
     *********************************************************************/
    public function __invoke(array $filters): LengthAwarePaginator
    {
        // -----------------------
        // Initialize Query With Relationships
        $query = MarketplacePayment::query()
            ->with(['user', 'order'])
            ->withCount(['order' => function ($query) {
                $query->withCount('orderItems');
            }]);


        // -----------------------
        // Apply Date Filter
        $query = $this->applyDateFilter($query, $filters);


        // -----------------------
        // Apply Status Filter
        if (!empty($filters['status'])) {
            $query->status($filters['status']);
        }


        // -----------------------
        // Apply Search Filter
        if (!empty($filters['searchTerm'])) {
            $query->search($filters['searchTerm']);
        }


        // -----------------------
        // Apply Sorting
        $query->sort(
            $filters['sortField'] ?? 'id',
            $filters['sortOrder'] ?? 'desc'
        );


        // -----------------------
        // Paginate Results
        return $query->paginate(
            $filters['perPage'] ?? config('pressbear.default_pagination_10', 15)
        )->withQueryString();
    }
}

<?php

namespace Domain\Payment\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Domain\Payment\Enum\PaymentStatus;
use Domain\Payment\Enum\PaymentSortableFields;

/*********************************************************************
 * ADMIN PAYMENT FILTER REQUEST
 *********************************************************************
 *
 * Validates all filter inputs for admin payment listing including:
 * - Search terms and pagination
 * - Status filtering with enum validation
 * - Date range filtering (preset and custom)
 * - Sorting with field validation
 *
 * Responsibilities:
 * - Validate search parameters
 * - Ensure status values are valid
 * - Validate date range inputs
 * - Verify sorting field and direction
 * - Sanitize pagination parameters
 *
 *********************************************************************/
class AdminPaymentFilterRequest extends FormRequest
{
    /*********************************************************************
     * AUTHORIZATION
     *********************************************************************
     *
     * Determine if the user is authorized to make this request
     *
     * @return bool
     *
     *********************************************************************/
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }


    /*********************************************************************
     * VALIDATION RULES
     *********************************************************************
     *
     * Get the validation rules that apply to the request
     *
     * @return array
     *
     *********************************************************************/
    public function rules(): array
    {
        return [
            // -----------------------
            // Search Parameters
            'search'        => 'nullable|string|max:100',

            // -----------------------
            // Status Filter
            'status'        => 'nullable|string|in:' . implode(',', PaymentStatus::filterableValues()),

            // -----------------------
            // Date Range Filters
            'preset_range'  => 'nullable|string|in:today,yesterday,last_7_days,last_30_days,last_90_days,last_12_months,custom,show_all',
            'start_date'    => 'nullable|date|required_if:preset_range,custom',
            'end_date'      => 'nullable|date|required_if:preset_range,custom|after_or_equal:start_date',

            // -----------------------
            // Sorting Parameters
            'sort_by'       => 'nullable|string|in:' . implode(',', PaymentSortableFields::values()),
            'sort_dir'      => 'nullable|string|in:asc,desc',

            // -----------------------
            // Pagination
            'per_page'      => 'nullable|integer|min:5|max:100',
        ];
    }


    /*********************************************************************
     * CUSTOM VALIDATION MESSAGES
     *********************************************************************
     *
     * Get custom validation error messages
     *
     * @return array
     *
     *********************************************************************/
    public function messages(): array
    {
        return [
            'status.in'         => 'The selected status is invalid.',
            'sort_by.in'        => 'The selected sort field is invalid.',
            'sort_dir.in'       => 'Sort direction must be either asc or desc.',
            'end_date.after_or_equal' => 'End date must be after or equal to start date.',
            'start_date.required_if'  => 'Start date is required when using custom date range.',
            'end_date.required_if'    => 'End date is required when using custom date range.',
        ];
    }


    /*********************************************************************
     * PREPARE FOR VALIDATION
     *********************************************************************
     *
     * Prepare the data for validation by setting defaults
     *
     * @return void
     *
     *********************************************************************/
    protected function prepareForValidation(): void
    {
        $this->merge([
            'sort_by'   => $this->input('sort_by', 'id'),
            'sort_dir'  => $this->input('sort_dir', 'desc'),
            'per_page'  => $this->input('per_page', 15),
        ]);
    }


    /*********************************************************************
     * GET VALIDATED FILTERS
     *********************************************************************
     *
     * Get validated and formatted filter data for domain classes
     *
     * @return array
     *
     *********************************************************************/
    public function getValidatedFilters(): array
    {
        $validated = $this->validated();

        return [
            'searchTerm'    => $validated['search'] ?? null,
            'status'        => $validated['status'] ?? null,
            'preset_range'  => $validated['preset_range'] ?? null,
            'start_date'    => $validated['start_date'] ?? null,
            'end_date'      => $validated['end_date'] ?? null,
            'sortField'     => $validated['sort_by'] ?? 'id',
            'sortOrder'     => $validated['sort_dir'] ?? 'desc',
            'perPage'       => $validated['per_page'] ?? 15,
        ];
    }
}

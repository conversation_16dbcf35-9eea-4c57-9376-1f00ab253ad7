<?php

namespace Domain\Payment\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AdminPaymentResource extends JsonResource
{
    /*********************************************************************
     * TRANSFORM RESOURCE TO ARRAY
     *********************************************************************
     *
     * Transform the payment resource into an array suitable for
     * admin interface consumption.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     *
     *********************************************************************/
    public function toArray($request): array
    {
        return [
            // -----------------------
            // Payment Core Data
            'id'                        => $this->id,
            'payment_amount'            => $this->payment_amount,
            'status'                    => $this->status,
            'external_transaction_id'   => $this->external_transaction_id,
            'created_at'                => $this->created_at,
            'updated_at'                => $this->updated_at,
            'created_at_formatted'      => $this->created_at_formatted,
            'updated_at_formatted'      => $this->updated_at_formatted,


            // -----------------------
            // User Relationship
            'user_id'                   => $this->user_id,
            'user'                      => $this->whenLoaded('user', function () {
                return [
                    'id'    => $this->user->id,
                    'name'  => $this->user->name,
                    'email' => $this->user->email,
                ];
            }),


            // -----------------------
            // Order Relationship
            'order'                     => $this->whenLoaded('order', function () {
                return [
                    'id'                    => $this->order->id,
                    'status'                => $this->order->status,
                    'price_paid'            => $this->order->price_paid,
                    'items_in_orders'       => $this->order->items_in_orders,
                    'order_items_count'     => $this->order->order_items_count ?? 0,
                ];
            }),


            // -----------------------
            // Order Count (when loaded via withCount)
            'order_count'               => $this->when(
                isset($this->order_count),
                $this->order_count
            ),
        ];
    }
}

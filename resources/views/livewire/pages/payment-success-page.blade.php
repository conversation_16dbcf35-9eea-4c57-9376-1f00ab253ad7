{{-- Main Body Wrapper --}}
<div id="app-body" class="flex justify-center">


    <div id="page-main-wrapper" class=" bg-gray-50 my-8 w-full md:w-1/2 h-auto rounded-lg mx-auto max-w-full 
                px-12 py-14 sm:px-8 
               lg:max-w-screen-2xl lg:px-16  ">


        <div class="pb-8 border-gray-300 flex flex-col items-center justify-center">


            <img class="h-64" src="{{ asset('graphics/illustrations/success.png') }}">

            <div class="mt-2 text-lg font-bold text-emerald-700 p-4 flex">
                <span class="h-6 w-6 mt-0.5">
                    <x-icons.lucide.check-circle />
                </span>
                <span id="payment-message" class="ml-2">
                    Payment Success
                </span>
            </div>


            {{-- Message --}}
            <div class="mb-8 mt-2 text-center text-gray-800">
                <p class="mb-1">
                    Your Payment was successful.
                </p>
                <p class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-orange-600" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                        </circle>
                        <path class="opacity-75" fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                        </path>
                    </svg>
                    Redirecting you to the order page in few seconds...
                </p>
            </div>


            {{-- Action Button --}}
            {{-- <a class="my-4">
                <button type="button"
                    class="disabled:opacity-50 px-4 py-2 text-sm font-medium text-white rounded-lg bg-orange-600 hover:bg-orange-700  dark:bg-primary dark:hover:bg-primary focus:outline-none  ">
                    Go To My Order
                </button>
            </a> --}}

        </div>
    </div>


    @script
    <script>
        document.addEventListener('livewire:initialized', function () {
                // Listen for new notifications
                window.Echo.private('App.Models.User.{{ auth()->id() }}')
                    .notification((notification) => {
                    window.location.href = notification.url;
                    });
            });
    </script>
    @endscript
</div>
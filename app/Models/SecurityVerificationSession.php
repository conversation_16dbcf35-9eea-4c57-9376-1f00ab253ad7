<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class SecurityVerificationSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'user_id',
        'email',
        'verification_code',
        'attempts',
        'verified',
        'verified_at',
        'expires_at',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'attempts' => 'integer',
    ];

    /**
     * Get the user that owns the verification session
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all verification attempts for this session
     */
    public function attempts(): HasMany
    {
        return $this->hasMany(SecurityVerificationAttempt::class, 'session_id', 'session_id');
    }

    /**
     * Check if the session is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the session is verified
     */
    public function isVerified(): bool
    {
        return $this->verified;
    }

    /**
     * Check if max attempts reached for this session
     */
    public function hasMaxAttemptsReached(): bool
    {
        return $this->attempts >= config('security.session.max_session_attempts', 5);
    }

    /**
     * Increment attempt counter
     */
    public function incrementAttempts(): void
    {
        $this->increment('attempts');
    }

    /**
     * Mark session as verified
     */
    public function markAsVerified(): void
    {
        $this->update([
            'verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Scope to get active (non-expired, non-verified) sessions
     */
    public function scopeActive($query)
    {
        return $query->where('verified', false)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get expired sessions
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Clean up expired sessions (for scheduled cleanup)
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Find active session by session_id and user_id
     */
    public static function findActiveSession(string $sessionId, int $userId): ?self
    {
        return static::where('session_id', $sessionId)
                    ->where('user_id', $userId)
                    ->active()
                    ->first();
    }
}

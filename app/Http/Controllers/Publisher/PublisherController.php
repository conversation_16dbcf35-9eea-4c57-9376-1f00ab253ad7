<?php

namespace App\Http\Controllers\Publisher;


use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Enums\OrderItemStates;
use App\Models\MarketplaceOrder;
use App\Models\WithdrawalRequest;
use App\Http\Controllers\Controller;
use Bavix\Wallet\Models\Transaction;
use Illuminate\Support\Facades\Auth;
use App\Models\MarketplaceAdminWebsite;
use App\Models\MarketplaceSingleOrderItem;

class PublisherController extends Controller
{
    /*********************************************************************
     * PUBLISHER DASHBOARD
     **********************************************************************
     *
     * Renders the publisher dashboard page with overview statistics
     * ..and recent activity.
     *
     * @return \Inertia\Response
     * Renders the publisher dashboard view with necessary data and statistics
     *
     *********************************************************************/
    public function dashboard(Request $request)
    {
        $filters = [
            'preset_range'  => $request->input('preset_range', 'show_all'),
            'start_date'    => $request->input('start_date'),
            'end_date'      => $request->input('end_date'),
        ];

        $baseAdminQuery = applyDateFilter(MarketplaceSingleOrderItem::query(), null, $filters);
        $baseWithdrawalsAdminQuery = applyDateFilter(auth('web')->user()->withdrawalRequests(), null, $filters);

        // Total Orders
        $orderStats['total'] = $baseAdminQuery->clone()->count();


        // Total Sales
        $sales = $baseAdminQuery->clone()->sum('publisher_payment_paid');
        $withdrawals = $baseWithdrawalsAdminQuery->clone()->sum('amount');
        $pending_payments = $sales - $withdrawals;

        // Format the numbers after calculations
        $orderStats['sales'] = number_format($sales, 2);
        $orderStats['withdrawals'] = number_format($withdrawals, 2);
        $orderStats['pending_payments'] = number_format($pending_payments, 2);



        // Orders by status
        foreach (OrderItemStates::cases() as $status) {
            $orderStats[$status->value] = $baseAdminQuery->clone()->where('state', $status->value)->count();
        }


        return Inertia::render('Publisher/Dashboard/Index', [
            'filters' => $filters,
            'orderStats' => $orderStats,
        ]);
    }




    /*********************************************************************
     * ORDERS OVERVIEW
     **********************************************************************
     *
     * Renders the orders overview page with list of all orders
     * ..and their current status.
     *
     * @return \Inertia\Response
     * Renders the orders overview view with list of orders and their status
     *
     *********************************************************************/
    public function orders()
    {
        // -----------------------
        // Render Orders View
        return Inertia::render('Publisher/Orders/Index');
    }





    /*********************************************************************
     * ORDER DETAILS
     **********************************************************************
     *
     * Renders the detailed order information page with specific
     * ..order details and related information.
     *
     * @return \Inertia\Response
     * Renders the order details view with specific order information
     *
     *********************************************************************/
    public function orderDetails()
    {
        // -----------------------
        // Render Order Details View
        return Inertia::render('Publisher/Orders/Details/Index');
    }



    /*********************************************************************
     * PAYMENTS OVERVIEW
     **********************************************************************
     *
     * Renders the payments overview page with payment history
     * ..and related financial information.
     *
     * @return \Inertia\Response
     * Renders the payments overview view with payment history and financial data
     *
     *********************************************************************/
    public function payments()
    {
        // -----------------------
        // Render Payments View
        return Inertia::render('Publisher/Payments/Index');
    }





    /*********************************************************************
     * SETTINGS MANAGEMENT
     **********************************************************************
     *
     * Renders the settings management page with configuration
     * ..options and account settings.
     *
     * @return \Inertia\Response
     * Renders the settings management view with configuration options and account settings
     *
     *********************************************************************/
    public function settings()
    {
        // -----------------------
        // Render Settings View
        return Inertia::render('Publisher/Settings/Index');
    }





    /*********************************************************************
     * WALLET OVERVIEW
     **********************************************************************
     *
     * Renders the wallet overview page with balance and transaction history.
     * Displays current balance and recent transactions.
     *
     * @return \Inertia\Response
     * Renders the wallet overview view with balance and transaction data
     *
     *********************************************************************/
    public function wallet()
    {
        $user = Auth::user();


        // Get recent transactions
        $transactions = $user->transactions()
            ->with('wallet', 'wallet.holder')
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'user' => $transaction->wallet->holder, // user who made the transaction
                    'order_id' => $transaction->meta['order_id'] ?? 'N/A', // order id if transaction is related to an order
                    'amount' => $transaction->amount,
                    'reference' => $transaction->meta['reference'] ?? 'N/A',
                    'status' => $transaction->confirmed ? 'Approved' : 'Pending',
                    'date' => $transaction->created_at->format('Y-m-d H:i:s'),
                    'debit' => $transaction->type === 'withdraw' ? number_format($transaction->amount / 100, 2) : '',
                    'credit' => $transaction->type === 'deposit' ? number_format($transaction->amount / 100, 2) : '',
                ];
            });

        // Get transaction statistics
        $stats = [
            'total_deposits' => $user->transactions()
                ->where('type', 'deposit')
                ->where('confirmed', true)
                ->sum('amount') / 100,
            'total_withdrawals' => $user->transactions()
                ->where('type', 'withdraw')
                ->where('confirmed', true)
                ->sum('amount') / 100,
            'pending_transactions' => $user->transactions()
                ->where('confirmed', false)
                ->count(),
        ];


        // -----------------------
        // Render Wallet View
        return Inertia::render('Wallet/Index', [
            'balance' => $user->user_balance,
            'transactions' => $transactions,
            'stats' => $stats,
        ]);
    }





    /*********************************************************************
     * WALLET DETAILS
     **********************************************************************
     *
     * Renders the detailed wallet information page with transaction history
     * ..and detailed balance information.
     *
     * @return \Inertia\Response
     * Renders the wallet details view with transaction history and balance details
     *
     *********************************************************************/
    public function walletTransactions(int $id)
    {
        $transaction = Transaction::find($id);

        // -----------------------
        // Render Wallet Details View
        return Inertia::render('Wallet/Details/Index', [
            'transaction' => $transaction,
        ]);
    }
}

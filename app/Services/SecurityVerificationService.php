<?php

namespace App\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\SecurityVerificationSession;
use App\Models\SecurityVerificationAttempt;

class SecurityVerificationService
{
    /**
     * Generate a signed and optionally encrypted cookie value
     */
    public function generateSecureCookieValue(array $payload): string
    {
        // Add timestamp and nonce for security
        $payload['timestamp'] = now()->toISOString();
        $payload['nonce'] = Str::random(16);

        $jsonPayload = json_encode($payload);

        // Note: Encryption disabled for simplicity - using signing only

        // Always sign the payload for security
        $signature = $this->generateSignature($jsonPayload);
        $securePayload = [
            'data' => $jsonPayload,
            'signature' => $signature
        ];
        return base64_encode(json_encode($securePayload));
    }

    /**
     * Validate and decode a secure cookie value
     */
    public function validateSecureCookieValue(string $cookieValue): ?array
    {
        try {
            $decoded = json_decode(base64_decode($cookieValue), true);

            if (!$decoded) {
                return null;
            }

            // Verify signature (always required)
            if (!isset($decoded['data']) || !isset($decoded['signature'])) {
                return null;
            }

            // Verify signature
            if (!$this->verifySignature($decoded['data'], $decoded['signature'])) {
                return null;
            }

            $payload = $decoded['data'];

            // Note: Decryption not needed since encryption is disabled

            $data = json_decode($payload, true);

            if (!$data || !isset($data['timestamp'])) {
                return null;
            }

            // Check expiration
            $timestamp = Carbon::parse($data['timestamp']);
            $expiresMinutes = config('security.cookie.expires_minutes', 1);

            if ($timestamp->addMinutes($expiresMinutes)->isPast()) {
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            \Log::warning('Failed to validate security cookie', [
                'error' => $e->getMessage(),
                'cookie_value' => substr($cookieValue, 0, 50) . '...'
            ]);
            return null;
        }
    }

    /**
     * Generate cryptographic signature for payload
     */
    private function generateSignature(string $payload): string
    {
        $key = config('app.key');
        $algorithm = config('security.crypto.signing_algorithm');

        return hash_hmac($algorithm, $payload, $key);
    }

    /**
     * Verify cryptographic signature
     */
    private function verifySignature(string $payload, string $signature): bool
    {
        $expectedSignature = $this->generateSignature($payload);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get dynamic cookie configuration based on environment
     */
    public function getCookieConfig(): array
    {
        $config = config('security.cookie');

        // Auto-detect domain and secure flag
        $config['domain'] = $this->detectCookieDomain();
        $config['secure'] = $this->shouldUseSecureCookies();

        return $config;
    }

    /**
     * Detect appropriate cookie domain based on current request
     */
    private function detectCookieDomain(): ?string
    {
        // Remove domain-specific logic to ensure consistent behavior across environments
        // Always return null to avoid domain-related cookie issues
        return null;
    }

    /**
     * Determine if secure cookies should be used
     */
    private function shouldUseSecureCookies(): bool
    {
        // Check if secure flag is explicitly set in config
        $configSecure = config('security.cookie.secure');
        if ($configSecure !== null) {
            return (bool) $configSecure;
        }

        if (!request()) {
            return false;
        }

        // Simplified logic: Use secure cookies for HTTPS, non-secure for HTTP
        // Remove environment-specific domain checking to ensure consistent behavior
        return request()->isSecure();
    }

    /**
     * Check if user has exceeded verification attempts
     */
    public function hasExceededAttempts(int $userId): bool
    {
        return SecurityVerificationAttempt::hasUserExceededGlobalRateLimit($userId);
    }

    /**
     * Record a failed verification attempt
     */
    public function recordFailedAttempt(int $userId, string $sessionId = null, string $attemptedCode = null, string $failureReason = null): void
    {
        SecurityVerificationAttempt::recordAttempt(
            userId: $userId,
            sessionId: $sessionId ?? 'unknown',
            attemptedCode: $attemptedCode,
            successful: false,
            failureReason: $failureReason,
            ipAddress: request()->ip(),
            userAgent: request()->userAgent()
        );
    }

    /**
     * Clear failed attempts for user (no longer needed with database approach)
     * Kept for backward compatibility but does nothing
     */
    public function clearFailedAttempts(int $userId): void
    {
        // Database approach doesn't need manual clearing
        // Failed attempts naturally expire based on time
    }

    /**
     * Create a new verification session
     */
    public function createVerificationSession(int $userId, string $email, string $verificationCode): SecurityVerificationSession
    {
        // Clean up any existing active sessions for this user
        SecurityVerificationSession::where('user_id', $userId)
            ->where('verified', false)
            ->delete();

        $sessionId = Str::uuid()->toString();
        $expiresMinutes = config('security.session.cache_expires_minutes', 15);

        return SecurityVerificationSession::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'email' => $email,
            'verification_code' => $verificationCode,
            'attempts' => 0,
            'verified' => false,
            'expires_at' => now()->addMinutes($expiresMinutes),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Get active verification session
     */
    public function getVerificationSession(string $sessionId, int $userId): ?SecurityVerificationSession
    {
        return SecurityVerificationSession::findActiveSession($sessionId, $userId);
    }

    /**
     * Verify code for a session
     */
    public function verifyCode(string $sessionId, int $userId, string $code): array
    {
        $session = $this->getVerificationSession($sessionId, $userId);

        if (!$session) {
            $this->recordFailedAttempt($userId, $sessionId, $code, 'session_not_found');
            return [
                'success' => false,
                'message' => 'Verification session expired or invalid',
                'code' => 'session_expired'
            ];
        }

        if ($session->hasMaxAttemptsReached()) {
            $this->recordFailedAttempt($userId, $sessionId, $code, 'max_session_attempts');
            return [
                'success' => false,
                'message' => 'Too many failed attempts for this session',
                'code' => 'max_attempts'
            ];
        }

        if ($session->verification_code !== $code) {
            $session->incrementAttempts();
            $this->recordFailedAttempt($userId, $sessionId, $code, 'invalid_code');
            return [
                'success' => false,
                'message' => 'Invalid verification code',
                'code' => 'invalid_code'
            ];
        }

        // Success - mark as verified and record successful attempt
        $session->markAsVerified();
        SecurityVerificationAttempt::recordAttempt(
            userId: $userId,
            sessionId: $sessionId,
            attemptedCode: $code,
            successful: true,
            ipAddress: request()->ip(),
            userAgent: request()->userAgent()
        );

        return [
            'success' => true,
            'message' => 'Verification successful',
            'session' => $session
        ];
    }

    /**
     * Update verification code for existing session (for resend)
     */
    public function updateVerificationCode(string $sessionId, int $userId, string $newCode): ?SecurityVerificationSession
    {
        $session = $this->getVerificationSession($sessionId, $userId);

        if (!$session) {
            return null;
        }

        // Reset attempts and update code
        $session->update([
            'verification_code' => $newCode,
            'attempts' => 0,
            'expires_at' => now()->addMinutes(config('security.session.cache_expires_minutes', 15))
        ]);

        return $session->fresh();
    }
}
